<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Fetch tickets from the database
$sql = "SELECT 
tk.ticket_number, tk.customer_number, cus.CusName AS customer_name,
tk.issue_details, tk.priority, tk.channel_type,
tk.`status`, tm.`name` AS assigne_team, tk.created_at
FROM 
tickets tk LEFT JOIN 
KCS_DB.Customers cus ON tk.customer_number=cus.CusCode
LEFT JOIN teams tm ON tk.assigned_team = tm.id
ORDER BY tk.created_at DESC limit 1000";

$result = $pdo->query($sql);

require_once '../../includes/header.php';
?>
<div class="container-fluid mt-4 small">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>Ticket List</h1>
        <div>
            <?php if ($_SESSION['userdata']['role'] === 'admin'): ?>
                <a href="import.php" class="btn btn-success mr-2">
                    <i class="fas fa-file-excel"></i> Import Tickets
                </a>
                <a href="import_history.php" class="btn btn-info mr-2">
                    <i class="fas fa-history"></i> Import History
                </a>
            <?php endif; ?>
            <a href="create.php" class="btn btn-primary">
                <i class="fas fa-plus"></i> Create Ticket
            </a>
        </div>
    </div>
    <table class="table table-striped table-hover" id="ticketList">
        <thead>
            <tr>
                <th>Ticket No.</th>
                <th>Customer ID</th>
                <th>Customer Name</th>
                <th style="width:500px;">Issue Detail</th>
                <th>Priority</th>
                <th>Assigned Team</th>
                <th>Status</th>
                <th>Created At</th>
                <th>Channel</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php if ($result->rowCount() > 0): ?>
                <?php while ($row = $result->fetch(PDO::FETCH_ASSOC)): ?>
                    <tr>
                        <td><?php echo $row['ticket_number']; ?></td>
                        <td><?php echo $row['customer_number']; ?></td>
                        <td><?php echo $row['customer_name']; ?></td>
                        <td><?php echo $row['issue_details']; ?></td>
                        <td>
                            <span class="badge badge-<?php echo getPriorityBadgeClass($row['priority']); ?>">
                                <?php echo htmlspecialchars($row['priority']); ?>
                            </span>
                        </td>
                        <td><?php echo $row['assigne_team']; ?></td>
                        <td><span class="badge badge-<?php echo getStatusBadgeClass($row['status']); ?>">
                                <?php echo htmlspecialchars($row['status']); ?>
                            </span>
                        </td>
                        <td><?php echo $row['created_at']; ?></td>
                        <td>
                            <?php 
                            $channel_types = [
                                '1' => 'Monitoring',
                                '2' => 'Telephone',
                                '3' => 'Email',
                                '4' => 'Chat',
                                '5' => 'Walk-in'
                            ];
                            echo isset($channel_types[$row['channel_type']]) ? 
                                htmlspecialchars($channel_types[$row['channel_type']]) : 
                                'Unknown';
                            ?>
                        </td>
                        <td>
                            <a href="view.php?tkt_id=<?php echo urlencode($row['ticket_number']); ?>" class="btn btn-sm btn-warning">
                                <i class="fas fa-eye"></i> View </a>
                            <a href="edit.php?tkt_id=<?php echo urlencode($row['ticket_number']); ?>" class="btn btn-sm btn-warning">
                                <i class="fas fa-edit"></i> Edit </a>
                        </td>
                    </tr>
                <?php endwhile; ?>
            <?php else: ?>
                <tr>
                    <td colspan="6">No tickets found.</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>
<?php
require_once '../../includes/main_script_loader.php';
require_once '../../includes/footer.php';
?>
