<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Replace the existing query with:
$query = "SELECT 
    t.*, 
    c.CusName AS customer_name,
    tm.name AS team_name,
    u.username AS created_by,
    CASE 
        WHEN t.status = 'Closed' THEN
            (SELECT changed_at 
             FROM ticket_status_history 
             WHERE ticket_number = t.ticket_number 
             AND status = 'Closed' 
             ORDER BY changed_at DESC 
             LIMIT 1)
        ELSE NULL
    END as closed_at,
    (SELECT SUM(TIMESTAMPDIFF(SECOND, 
        tsh1.changed_at, 
        COALESCE(tsh2.changed_at, CURRENT_TIMESTAMP)))
     FROM ticket_status_history tsh1
     LEFT JOIN ticket_status_history tsh2 
        ON tsh1.ticket_number = tsh2.ticket_number
        AND tsh2.id = (
            SELECT MIN(id) 
            FROM ticket_status_history 
            WHERE ticket_number = tsh1.ticket_number 
            AND id > tsh1.id
        )
     WHERE tsh1.ticket_number = t.ticket_number
     AND tsh1.status = 'Pending'
    ) as pending_seconds
FROM 
    tickets t
    LEFT JOIN KCS_DB.Customers c ON t.customer_number = c.CusCode
    LEFT JOIN teams tm ON t.assigned_team = tm.id
    LEFT JOIN users u ON t.username = u.username
ORDER BY t.created_at DESC LIMIT 500";

$result = $pdo->query($query);

require_once '../../includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="card shadow">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Ticket Reports</h4>
            <button class="btn btn-outline-light" onclick="exportToExcel('ticketReport')">
                <i class="fas fa-download"></i> Export to Excel
            </button>
        </div>
        <div class="card-body">
            <?php if ($result->rowCount() > 0): ?>
                <div class="table-responsive small">
                    <table class="table table-striped table-hover" id="ticketReport">
                        <thead>
                            <tr>
                                <th>Ticket #</th>
                                <th>Customer</th>
                                <th width="500px">Issue Details</th>
                                <th>Priority</th>
                                <th>Status</th>
                                <th>Assigned Team</th>
                                <th>Created By</th>
                                <th>Created At</th>
                                <th>Working Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($row = $result->fetch(PDO::FETCH_ASSOC)): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($row['ticket_number']); ?></td>
                                    <td>
                                        <?php echo htmlspecialchars($row['customer_name']); ?>
                                        <small class="d-block text-muted">
                                            <?php echo htmlspecialchars($row['customer_number']); ?>
                                        </small>
                                    </td>
                                    <td><?php echo htmlspecialchars($row['issue_details']);?></td>
                                    <td>
                                        <span class="badge badge-<?php echo getPriorityBadgeClass($row['priority']); ?>">
                                            <?php echo htmlspecialchars($row['priority']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?php echo getStatusBadgeClass($row['status']); ?>">
                                            <?php echo htmlspecialchars($row['status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($row['team_name']); ?></td>
                                    <td><?php echo htmlspecialchars($row['created_by']); ?></td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($row['created_at'])); ?></td>
                                    <td>
                                        <?php
                                            $working_time = calculateWorkingTime(
                                                $row['created_at'],
                                                $row['closed_at'],
                                                $row['pending_seconds'] ?? 0,
                                                $row['status']
                                            );
                                        ?>
                                        <span class="badge badge-info">
                                            <?php echo $working_time['formatted']; ?>
                                        </span>
                                        <?php
                                            if ($row['status'] == 'Pending') {
                                                    echo ' <small class="d-block text-muted">(Paused)</small>';
                                                }elseif ($row['status'] !== 'Closed') {
                                                    echo ' <small class="d-block text-muted">(Running)</small>';
                                                }
                                        ?>
                                      
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-info">No tickets found.</div>
            <?php endif; ?>
        </div>
    </div>
</div>

<div id="reportHelpNote" class="position-fixed shadow-sm" style="width: 300px; z-index: 1000; right: 20px; top: 80px;">
    <div class="card note-card">
        <div class="card-header note-header py-2" id="helpDragHandle">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-question-circle"></i> Report Guide
                </h6>
                <button type="button" class="btn btn-link btn-sm text-secondary p-0" id="minimizeHelp">
                    <i class="fas fa-plus"></i>
                </button>
            </div>
        </div>
        <div class="card-body note-body py-2" id="helpContent" style="display: none;">
        <div class="help-item mb-2">
                <strong><i class="fas fa-clock text-info"></i> Working Time:</strong>
                <ul class="small pl-4 mb-2">
                    <li>เริ่มนับเวลาตั้งแต่สร้าง Ticket</li>
                    <li>หยุดนับเมื่อ Status = Closed</li>
                    <li>ไม่นับเวลาช่วง Status = Pending</li>
                </ul>
            </div>
            <div class="help-item mb-2">
                <strong><i class="fas fa-tags text-warning"></i> Status Types:</strong>
                <ul class="small pl-4 mb-2">
                    <li>Open - เพิ่งสร้าง Ticket</li>
                    <li>In Progress - กำลังดำเนินการ</li>
                    <li>Pending - รอข้อมูลเพิ่มเติม</li>
                    <li>Closed - ดำเนินการเสร็จสิ้น</li>
                </ul>
            </div>
            <div class="help-item">
                <strong><i class="fas fa-download text-success"></i> Export Tips:</strong>
                <ul class="small pl-4 mb-0">
                    <li>คลิกปุ่ม Export เพื่อดาวน์โหลด Excel</li>
                    <li>รองรับการ Export ข้อมูลทั้งหมด</li>
                    <li>สามารถกรองข้อมูลก่อน Export ได้</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function exportToExcel(tableID) {
    let table = document.getElementById(tableID);
    let html = table.outerHTML;
    let url = 'data:application/vnd.ms-excel,' + encodeURIComponent(html);
    let downloadLink = document.createElement("a");
    document.body.appendChild(downloadLink);
    downloadLink.href = url;
    downloadLink.download = 'ticket_report.xls';
    downloadLink.click();
    document.body.removeChild(downloadLink);
}
</script>

<?php
require_once '../../includes/main_script_loader.php';
?>
<script src="<?=BASE_URL?>/assets/js/reports-ticket.js?v=<?=$timestamp?>"></script>
<?php
require_once '../../includes/footer.php';
?>
