<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';

require_once '../../vendor/autoload.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require_once '../../includes/functions.php';

// Fetch ticket ID from URL
$ticket_id = $_GET['tkt_id'] ?? 0;

$interim_mailto = '<EMAIL>,<EMAIL>';

// Fetch ticket details with JOIN

$query_ticket = "SELECT 
    tk.*, 
    c.<PERSON>us<PERSON>ame AS customer_name,
    c<PERSON><PERSON>us<PERSON>ddress AS customer_address,
    u2.username AS assigned_worker_username,
    u2.fullname AS assigned_worker_name,
    u.username AS created_by,
    pt.Connection_Type AS producttype,
    pj.Project_Name,
    pj.AutoEmail,
    pj.IsAutoEmail,
    m.Phone1 AS Customer_contact,
    m.Site_Name,m.Site_Address
FROM 
    (SELECT * FROM tickets t WHERE t.ticket_number =?)as tk
    LEFT JOIN KCS_DB.Main m ON tk.csno = m.Ref
    LEFT JOIN KCS_DB.Customers c ON tk.customer_number = c.CusCode
    LEFT JOIN KCS_DB.Connection_Type pt ON m.Connection_Type = pt.`Connection_ID`
    LEFT JOIN KCS_DB.Project pj ON m.Project_ID = pj.Project_ID
    LEFT JOIN users u ON tk.username = u.username
    LEFT JOIN users u2 ON tk.assigned_worker = u2.username";

$stmt = $pdo->prepare($query_ticket);
$stmt->execute([$ticket_id]);
$ticket = $stmt->fetch(PDO::FETCH_ASSOC);

$site_login_name='-';
$login_csdb = get_login_csdb($ticket['csno']);
if ($login_csdb) {
    $site_login_name = $login_csdb['Login'] ?? '-';
}                                        
//var_dump($ticket);
// Fetch symptoms and product types for reference
$symptoms_query = "SELECT code, name FROM symptoms WHERE is_active = 1";
$symptoms_stmt = $pdo->query($symptoms_query);
$symptoms_list = $symptoms_stmt->fetchAll(PDO::FETCH_KEY_PAIR);

$product_types_query = "SELECT code, name FROM product_types WHERE is_active = 1";
$product_types_stmt = $pdo->query($product_types_query);
$product_types_list = $product_types_stmt->fetchAll(PDO::FETCH_KEY_PAIR);

// Fetch channel types for reference
$channel_types_query = "SELECT id, name FROM channel_types WHERE is_active = 1";
$channel_types_stmt = $pdo->query($channel_types_query);
$channel_types_list = $channel_types_stmt->fetchAll(PDO::FETCH_KEY_PAIR);

// Fetch ticket comments
$comments_query = "SELECT 
    tc.*, u.username, 
    (SELECT tm.`name` FROM 
        team_members tmm LEFT JOIN teams tm ON tmm.team_id = tm.id
        WHERE tmm.user_id=tc.user_id LIMIT 1) AS team
    FROM ticket_comments tc
    LEFT JOIN users u ON tc.user_id = u.id
    WHERE tc.ticket_number = ?
    ORDER BY tc.created_at DESC";

$comments_stmt = $pdo->prepare($comments_query);
$comments_stmt->execute([$ticket_id]);
$comments = $comments_stmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch extended ticket information
$extended_query = "SELECT * FROM ticket_extended WHERE ticket_number = ?";
$extended_stmt = $pdo->prepare($extended_query);
$extended_stmt->execute([$ticket_id]);
$extended_info = $extended_stmt->fetch(PDO::FETCH_ASSOC);
$cause_detail = $extended_info['cause_detail'] ?? '';
$cause_detail_en = $extended_info['cause_detail_eng'] ?? '';

// Fetch all workers for the forward dropdown
$workers_query = "SELECT username, fullname, email FROM users 
                  WHERE status='active' AND division='Operation' AND department='Customer Support'
                  ORDER BY fullname";
$workers_stmt = $pdo->query($workers_query);
$workers = $workers_stmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch all interim information
$interim_query = "SELECT * FROM ticket_interim WHERE ticket_number = ?";
$interim_stmt = $pdo->prepare($interim_query);
$interim_stmt->execute([$ticket_id]);
$interim_data = $interim_stmt->fetch(PDO::FETCH_ASSOC);

$request_interim = $_POST['request_interim'] ?? 1;
$button_text_interim = 'Request Interim';

if ($ticket['have_interim'] && $interim_data) {
    $button_text_interim = 'Uninstall Interim';
    $request_interim = 0;
}

// Handle new comment submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['comment'])) {

    $comment = base64_encode(trim($_POST['comment']));
    if (!empty($comment)) {
        $insert_query = "INSERT INTO ticket_comments (ticket_number, user_id, comment_base64, created_at) 
                        VALUES (?, ?, ?, CURRENT_TIMESTAMP)";

        $insert_stmt = $pdo->prepare($insert_query);
        $insert_stmt->execute([$ticket_id, $_SESSION['user_id'], $comment]);

        // Redirect to prevent form resubmission
        header("Location:" . BASE_URL . "/modules/tickets/ticket.php?tkt_id=" . $ticket_id);
        exit();
    }
}

// Handle status update submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['status']) && isset($_POST['action']) && $_POST['action'] === 'update_status') {

    $new_status = trim($_POST['status']);
    $current_status = trim($_POST['current_status'] ?? '');
    $status_comment = trim($_POST['status_comment'] ?? '');
    $sub_status = trim($_POST['sub_status'] ?? '');
    $send_email = $_POST['send_email'] ?? '0';
    $ticket_id = $_POST['ticket_number'];
    $cause_name_en_email = trim($_POST['cause_name_en_email'] ?? '');
    $fault_cleared = $_POST['fault_cleared'] ?? '0';
    $cause_detail_template_id = trim($_POST['cause_detail_template_id'] ?? 0);
    $cause_name = trim($_POST['cause_name'] ?? '');
    $cause_name_en = trim($_POST['cause_name_en'] ?? '');

    if (empty($new_status) || ($new_status == $current_status && $sub_status == $ticket['sub_status'])) {
        $error_message = "Invalid status update.";
        // Redirect to prevent form resubmission
        header("Location:" . BASE_URL . "/modules/tickets/ticket.php?tkt_id=" . $ticket_id . "&status_updated=0&error=" . urlencode($error_message));
        exit();
    } else {

        try {
            // Start transaction
            $pdo->beginTransaction();

            $closted_at = $ticket['closed_at'] ?? null;
            $closted_by = '';
            $updated_by = $_SESSION['userdata']['username'];
            $open_at = $ticket['open_at'] ?? date('Y-m-d H:i:s');

            if ($new_status === 'Closed') {
                $tmp_closed_at = $_POST['closed_at'] ?? date('Y-m-d H:i:s');
                if (!empty($tmp_closed_at)) {
                    $closted_at = date('Y-m-d H:i:s', strtotime($tmp_closed_at));
                }
                $closted_by = $updated_by;
            } else if ($new_status === 'Open') {
                $tmp_open_at = $_POST['open_at'] ?? '';
                if (!empty($tmp_open_at)) {
                    $open_at = date('Y-m-d H:i:s', strtotime($tmp_open_at));
                }
            }

            // Update ticket status and sub_status
            $update_query = "UPDATE tickets SET status = ?, sub_status = ?,updated_by = ?, updated_at = CURRENT_TIMESTAMP,closed_at =?,closed_by=?,open_at=?,is_fault_cleared=?,cause_detail_template_id=? WHERE ticket_number = ?";
            $update_stmt = $pdo->prepare($update_query);
            $update_stmt->execute([$new_status, $sub_status, $updated_by, $closted_at, $closted_by, $open_at, $fault_cleared, $cause_detail_template_id, $ticket_id]);

            // Update ticket causes
            $update_query = "REPLACE INTO  ticket_extended(ticket_number,cause_detail,cause_detail_eng,updated_at,updated_by) 
            VALUES(?,?,?,CURRENT_TIMESTAMP,?)";
            $update_stmt = $pdo->prepare($update_query);
            $update_stmt->execute([
                $ticket_id,
                $cause_name,
                $cause_name_en,
                $_SESSION['user_id']
            ]);

            // Record status change in history
            $history_query = "INSERT INTO ticket_status_history 
            (ticket_number, status, sub_status, changed_by, comments) 
            VALUES (?, ?, ?, ?, ?)";
            $history_stmt = $pdo->prepare($history_query);
            $history_stmt->execute([
                $ticket_id,
                $new_status,
                $sub_status,
                $_SESSION['user_id'],
                $status_comment
            ]);
            $to_open_at = date('Y-m-d H:i:s', strtotime($open_at));

            if ($to_open_at !== $ticket['open_at'] && $new_status === 'Open') {
                $history_query = "INSERT INTO ticket_status_history 
                    (ticket_number, status, changed_by, comments) 
                    VALUES (:ticket_number, :status, :changed_by, :comments)";
                $history_stmt = $pdo->prepare($history_query);
                $history_stmt->execute([
                    ':ticket_number' => $ticket_id,
                    ':status' => $new_status,
                    ':changed_by' => $_SESSION['user_id'],
                    ':comments' => "Open time updated(open_at was changed from " . $ticket['open_at'] . " to " . $to_open_at . ")"
                ]);
            }

            $pdo->commit();
            //reload ticket details
            $stmt = $pdo->prepare($query_ticket);
            $stmt->execute([$ticket_id]);
            $ticket = $stmt->fetch(PDO::FETCH_ASSOC);

            $mailto = $ticket['AutoEmail'] ?? '';
            $send_success = false;
            $emailBody = '';
            $emailSubject = '';
            if ($send_email == '1' && $ticket['IsAutoEmail'] == 1 && $mailto !== '') {
                if ($new_status === 'Closed') {
                    //send notification email if status is 'Closed'
                    $action_details = "Date Resolve: " . $ticket['closed_at'];
                    $cause_name_en = trim($_POST['cause_name_en'] ?? '');
                    $cause_detail_msg  = "Cause of failure: " . $cause_name_en;
                    $emailBody = generateEmailTemplate_Ticket($ticket, $cause_detail_msg, $status_comment, $action_details);
                    $emailSubject = "Ref.Ticket : {$ticket_id} Service Number: {$ticket['csno']} Ticket has been closed.";
                    $send_success = sendNotifyEmail($mailto, $emailSubject, $emailBody);
                } else if ($new_status === 'In Progress') {
                    // Send email notification for In Progress status
                    $action_details = "Date Issue: " . $ticket['open_at'];
                    $cause_detail_msg  = "Symptoms : " . $symptoms_list[$ticket['symptoms_details']] ?? 'N/A';
                    $emailBody = generateEmailTemplate_Ticket($ticket, $cause_detail_msg, $status_comment, $action_details);
                    $emailSubject = "Ref.Ticket : {$ticket_id} Service Number: {$ticket['csno']} Ticket has been opened.";
                    $send_success = sendNotifyEmail($mailto, $emailSubject, $emailBody);
                } //end if

            } //chk can send email
            $send_msg = '';
            if ($send_email == '1') {
                if ($send_success) {
                    $send_msg = 'notification email successfully';
                } else {
                    if ($mailto === '') {
                        $send_msg = 'No email address found for notification';
                    } else {
                        $send_msg = 'email notification failed';
                    }
                    if ($ticket['IsAutoEmail'] !== 1) {
                        $send_msg .= ' (AutoEmail is disabled on database)';
                    }
                }
            }

            // Log email sending
            //var_dump('Email sent: ' . $send_msg, 'to: ' . $mailto, 'subject: ' . $emailSubject, 'body: ' . $emailBody);
            // Redirect to prevent form resubmission
            header("Location:" . BASE_URL . "/modules/tickets/ticket.php?tkt_id=" . $ticket_id . "&status_updated=1&send_msg=" . urlencode($send_msg));
            exit();
        } catch (Exception $e) {
            $pdo->rollBack();
            $error_message = "Error updating status : " . $e->getMessage();
        }
    } //check if status is empty or same as current status
}

// Handle ticket forwarding
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'forward_ticket') {
    $forward_worker = trim($_POST['forward_worker'] ?? '');
    $forward_priority = trim($_POST['forward_priority'] ?? '');
    $forward_notes = trim($_POST['forward_notes'] ?? '');
    $ticket_number = trim($_POST['ticket_number'] ?? '');

    if (empty($forward_worker) || empty($forward_notes) || empty($ticket_number)) {
        $error_message = "All fields are required for forwarding.";
        // Redirect with error
        header("Location:" . BASE_URL . "/modules/tickets/ticket.php?tkt_id=" . $ticket_id . "&forward_error=" . urlencode($error_message));
        exit();
    }

    try {
        // Start transaction
        $pdo->beginTransaction();

        // 1. Update the ticket's Assigned Staff and priority
        $update_query = "UPDATE tickets SET 
                        assigned_worker = ?, 
                        priority = ?,
                        updated_at = CURRENT_TIMESTAMP 
                        WHERE ticket_number = ?";
        $update_stmt = $pdo->prepare($update_query);
        $update_stmt->execute([$forward_worker, $forward_priority, $ticket_number]);

/*         // 2. Get the worker name for the comment
        $worker_query = "SELECT fullname FROM users WHERE username = ?";
        $worker_stmt = $pdo->prepare($worker_query);
        $worker_stmt->execute([$forward_worker]);
        $worker_name = $worker_stmt->fetchColumn(); */

        // 3. Add a system comment about the forwarding
        $forward_comment = "<div class='alert alert-info'>
            <strong>Ticket Forwarded</strong><br>
            This ticket has been forwarded to <strong>{$forward_worker}</strong> by " .
            htmlspecialchars($_SESSION['userdata']['username']) . ".<br>
            <strong>Notes:</strong> " . nl2br(htmlspecialchars($forward_notes)) . "
        </div>";

        $comment_query = "INSERT INTO ticket_comments 
                        (ticket_number, user_id, comment, created_at, is_system) 
                        VALUES (?, ?, ?, CURRENT_TIMESTAMP, 1)";
        $comment_stmt = $pdo->prepare($comment_query);
        $comment_stmt->execute([$ticket_number, $_SESSION['user_id'], $forward_comment]);

        // 4. Record in ticket_forwards table
        $forward_query = "INSERT INTO ticket_forwards 
                        (ticket_number, from_worker, to_worker, forwarded_by, notes, created_at) 
                        VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)";
        $forward_stmt = $pdo->prepare($forward_query);
        $forward_stmt->execute([
            $ticket_number,
            $ticket['assigned_worker'],
            $forward_worker,
            $_SESSION['user_id'],
            $forward_notes
        ]);

        // 5. Send notification to the worker (optional)
        $worker_email_query = "SELECT email FROM users WHERE username = ?";
        $worker_email_stmt = $pdo->prepare($worker_email_query);
        $worker_email_stmt->execute([$forward_worker]);
        $worker_email = $worker_email_stmt->fetchColumn();

        if ($worker_email) {
            // Send email notification (implement your email function)
            $subject = "Ticket #{$ticket_number} has been forwarded to you";
            $message = "A ticket has been forwarded to you.\n\n";
            $message .= "Ticket : #{$ticket_number}\n";
            $message .= "Customer : {$ticket['customer_name']}\n";
            $message .= "Notes : {$forward_notes}\n\n";
            $message .= "Please review this ticket at : " . BASE_URL . "/modules/tickets/ticket.php?tkt_id={$ticket_number}";

            // Uncomment when you have an email function
            // sendEmail($worker_email, $subject, $message);
        }

        $pdo->commit();

        // Redirect to prevent form resubmission
        header("Location:" . BASE_URL . "/modules/tickets/ticket.php?tkt_id=" . $ticket_id . "&forward_success=1");
        exit();
    } catch (Exception $e) {
        $pdo->rollBack();
        $error_message = "Error forwarding ticket : " . $e->getMessage();
        header("Location:" . BASE_URL . "/modules/tickets/ticket.php?tkt_id=" . $ticket_id . "&forward_error=" . urlencode($error_message));
        exit();
    }
}

// Handle interim form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_interim') {
    try {
        // Check if record exists
        if ($interim_data) {
            // Update existing record
            $update_query = "UPDATE ticket_interim SET 
                                mailto = ?, 
                                login_temp = ?, 
                                sim_no = ?, 
                                sim_serial = ?, 
                                sim_operator = ?, 
                                sim_package = ?,
                                updated_at = CURRENT_TIMESTAMP,
                                updated_by = ?
                                WHERE ticket_number = ?";

            $update_stmt = $pdo->prepare($update_query);
            $update_stmt->execute([
                $_POST['mailto'] ?? null,
                $_POST['login_temp'] ?? null,
                $_POST['sim_no'] ?? null,
                $_POST['sim_serial'] ?? null,
                $_POST['sim_operator'] ?? null,
                $_POST['sim_package'] ?? null,
                $_SESSION['user_id'],
                $ticket_id
            ]);
        } else {
            // Insert new record
            $insert_query = "INSERT INTO ticket_interim 
            (
            ticket_number, 
            mailto, 
            login_temp, 
            sim_no, 
            sim_serial, 
            sim_operator, 
            sim_package,
            created_at,
            created_by
           ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?)";

            $insert_stmt = $pdo->prepare($insert_query);
            $insert_stmt->execute([
                $ticket_id,
                $_POST['mailto'] ?? null,
                $_POST['login_temp'] ?? null,
                $_POST['sim_no'] ?? null,
                $_POST['sim_serial'] ?? null,
                $_POST['sim_operator'] ?? null,
                $_POST['sim_package'] ?? null,
                $_SESSION['user_id']
            ]);
        } // Check if interim data exists

        // update ticket have interim
        $update_ticket_interim = "UPDATE tickets SET have_interim = ? WHERE ticket_number = ?";
        $update_interim_stmt = $pdo->prepare($update_ticket_interim);
        $update_interim_stmt->execute([$request_interim, $ticket_id]);

        $interim_request_text = 'Terminate';
        if ($request_interim == 1) {
            $interim_request_text = 'Active';
        } 
        // Prepare email content
        $emailSubject = "Ticket Number : {$ticket_id} [{$interim_request_text} Sim] 4G Internet สำหรับงาน MA";
        $BASE_URL = BASE_URL;
$emailBody = <<<EOD
<html><body><p>
        <strong>Title :</strong> [{$interim_request_text} Sim] 4G Internet สำหรับงาน MA <br/>
        <strong>Dear</strong> Carrier,
        <br/><br/>
        <strong>Ticket Number :</strong> <a href="{$BASE_URL}/modules/tickets/ticket.php?tkt_id={$ticket_id}">{$ticket_id}</a> <br />
        <strong>Sim S/N :</strong> {$_POST['sim_serial']} <br/>
        <strong>Sim No. :</strong> {$_POST['sim_no']} <br/>
        <strong>Sim Operator :</strong> {$_POST['sim_operator']} <br/>
        <strong>Package :</strong> {$_POST['sim_package']} <br/>
        <strong>Login Temp :</strong> {$_POST['login_temp']} <br/>
        <br/>
        <strong>CS No. :</strong> {$ticket['csno']} <br/>
        <strong>Login :</strong> {$site_login_name} <br/>
        <strong>Location :</strong> {$ticket['Site_Name']}<br/>
        <strong>Detail :</strong> รบกวน {$interim_request_text} SIM MA นำไปใช้ติดตั้งสำรองให้ลูกค้าใช้งานชั่วคราว <br/></p>
        <p><br/><strong>Best Regards,</strong> <br/>
            <br/><strong>Customer Support Department</strong> <br/>
            <em>Tel. : 1605, +************ #2 </em><br/>
            <em>Email : <a href="mailto:<EMAIL>"><EMAIL></a></em> </p>
    </body></html>
EOD;
       
        // Send email
        $emailSent = sendInterimEmail($_POST['mailto'], $emailSubject, $emailBody);

        if (!$emailSent) {
            // Log email sending failure but don't stop the process
            error_log("Failed to send interim update email for ticket #{$ticket_id}");
        }

        // Add email status to system comment
        $interim_comment = "<div class='alert alert-info'>
                            <strong>$button_text_interim</strong><br>
                            Interim details have been updated by " . htmlspecialchars($_SESSION['userdata']['username']) . ".<br>" .
            ($emailSent ? "<span class='text-success'>✓ Notification email sent [" . $_POST['mailto'] . "]</span>"  :
                "<span class='text-warning'>⚠ Email notification failed</span>") .
            "</div>";

        $comment_query = "INSERT INTO ticket_comments 
                        (ticket_number, user_id, comment, created_at, is_system) 
                        VALUES (?, ?, ?, CURRENT_TIMESTAMP, 1)";
        $comment_stmt = $pdo->prepare($comment_query);
        $comment_stmt->execute([$ticket_id, $_SESSION['user_id'], $interim_comment]);
        // Redirect to the ticket page with success message
        echo "<script>
            setTimeout(function() {
                window.location.href = 'ticket.php?tkt_id=" . $ticket_id . "&interim_updated=1';
            }, 2000); // 2000 milliseconds = 2 seconds
        </script>";
        exit();
    } catch (Exception $e) {
        $error_message = "Error updating interim information : " . $e->getMessage();
        var_dump($error_message);
        exit();
    }
}

/*
// Fetch causes for dropdowns using only 'title'
$main_causes = $pdo->query("SELECT title FROM ticket_main_cause ORDER BY title")->fetchAll(PDO::FETCH_COLUMN);
$sub_causes = $pdo->query("SELECT title FROM ticket_sub_cause ORDER BY title")->fetchAll(PDO::FETCH_COLUMN);
$issue_causes = $pdo->query("SELECT title FROM ticket_issue_cause ORDER BY title")->fetchAll(PDO::FETCH_COLUMN);
$group_causes = $pdo->query("SELECT title FROM ticket_group_cause ORDER BY title")->fetchAll(PDO::FETCH_COLUMN);
*/

require_once '../../includes/header.php';

// Display status update success message
if (isset($_GET['status_updated'])) {
    if ($_GET['status_updated'] == 0) {
        echo '<div class="alert alert-danger alert-dismissible fade show">
            ' . htmlspecialchars($_GET['error']) . '<br>' . htmlspecialchars(urldecode($_GET['send_msg'] ?? '')) . '
            <button type="button" class="close" data-dismiss="alert">&times;</button>
          </div>';
    } else {
        echo '<div class="alert alert-success alert-dismissible fade show">
            Status updated successfully <br>' . htmlspecialchars(urldecode($_GET['send_msg'] ?? '')) . '
            <button type="button" class="close" data-dismiss="alert">&times;</button>
          </div>';
    }
}
if (isset($_GET['cause_updated'])) {
    if ($_GET['cause_updated'] == 0) {
        echo '<div class="alert alert-danger alert-dismissible fade show">
            ' . htmlspecialchars($_GET['cause_error']) . '
            <button type="button" class="close" data-dismiss="alert">&times;</button>
          </div>';
    } else {
        echo '<div class="alert alert-success alert-dismissible fade show">
            Cause updated successfully
            <button type="button" class="close" data-dismiss="alert">&times;</button>
          </div>';
    }
}
// Display forwarding success/error messages
if (isset($_GET['forward_success'])) {
    echo '<div class="alert alert-success alert-dismissible fade show">
        Ticket successfully forwarded to another team.
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>';
}
if (isset($_GET['forward_error'])) {
    echo '<div class="alert alert-danger alert-dismissible fade show">
        ' . htmlspecialchars($_GET['forward_error']) . '
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>';
}
// Display interim update success message
if (isset($_GET['interim_updated'])) {
    echo '<div class="alert alert-success alert-dismissible fade show">
        Interim information updated successfully
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>';
}
// Add this where you handle other alerts
if (isset($_GET['email_sent'])) {
    echo '<div class="alert alert-success alert-dismissible fade show">
        Interim information updated and notification email sent successfully.
        <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>';
}
if (isset($_GET['email_failed'])) {
    echo '<div class="alert alert-warning alert-dismissible fade show">
        Interim information updated but email notification failed to send.
        <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>';
}
if (isset($_GET['success'])) {
    echo '<div class="alert alert-success alert-dismissible fade show">
        ' . $_GET['success'] . '
        <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>';
}

// If no workers were found, create a fallback
if (empty($workers)) {
    // Log this issue
    error_log("No workers found in the database for ticket assignment");

    // Create a fallback array with at least the current worker
    if (!empty($ticket['assigned_worker'])) {
        $workers = [
            [
                'username' => $ticket['assigned_worker'],
                'fullname' => $ticket['assigned_worker_name'] ?? 'Current Worker'
            ]
        ];
    }
}

?>

<style>
    .overdue {
        cursor: pointer;
        animation: blink 1s linear infinite;
    }

    .interim {
        cursor: pointer;
        animation: blink 1s linear infinite;
    }

    @keyframes blink {
        50% {
            opacity: 0.5;
        }
    }
</style>
<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-md-11">
            <?php if (!$ticket) : ?>
                <div class="alert alert-danger">
                    Ticket not found. <a href="list.php" class="alert-link">Return to ticket list</a>
                </div>
            <?php else : ?>
                <div class="card shadow">
                    <!-- Ticket Header Title -->
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <h4 class="mb-0 mr-2">Ticket #<?php echo htmlspecialchars($ticket['ticket_number']); ?></h4>
                            <span class="badge badge-<?php echo getStatusBadgeClass($ticket['status']); ?>">
                                <?php
                                $status = $ticket['status'] ?? '';
                                if ($status === 'Pending') {
                                    echo 'On Hold(Stop Clock)';
                                } else {
                                    echo htmlspecialchars($status);
                                }

                                ?>
                            </span>
                            <?php
                            if ($ticket['status'] !== 'Closed') {
                                $updated_time = strtotime($ticket['updated_at']);
                                $current_time = time();
                                $hours_diff = ($current_time - $updated_time) / 3600;
                                if ($hours_diff >= 2) {
                                    echo '<br> <span class="badge badge-danger overdue ml-2" title="No any update ' . round($hours_diff, 1) . ' hrs ago">Overdue</span>';
                                }
                                //check interim status
                                if ($ticket['have_interim'] === 1) {
                                    echo '<span class="badge badge-warning ml-2 interim" title="Interim is active"><i class="fas fa-bell"></i> Interim Active</span>';
                                }
                            }
                            ?>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-light mr-2" data-toggle="modal" data-target="#extendedInfoModal">
                                <i class="fas fa-info-circle"></i> Extended Info
                            </button>
                            <button type="button" class="btn btn-sm btn-info mr-2" id="showHelpBtn">
                                <i class="fas fa-question-circle"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Customer Information Section -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Customer Information</h5>
                                <table class="table table-sm">
                                    <tr>
                                        <th width="150">Customer Name :</th>
                                        <td colspan="3"><?php echo htmlspecialchars($ticket['customer_name'] ?? 'N/A'); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Customer No. :</th>
                                        <td><?php echo htmlspecialchars($ticket['customer_number']); ?></td>
                                        <th width="150">CS No.(Ref) :</th>
                                        <td><?php echo htmlspecialchars($ticket['csno'] ?? 'N/A'); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Contact Person :</th>
                                        <td colspan="3">
                                            <?php echo htmlspecialchars($ticket['Customer_contact'] ?? '-'); ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Site Name :</th>
                                        <td colspan="3">
                                            <?php echo htmlspecialchars($ticket['Site_Name'] ?? '-'); ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Address :</th>
                                        <td colspan="3">
                                            <Address class="small"><?php echo htmlspecialchars($ticket['Site_Address'] ?? 'N/A'); ?></Address>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Ticket Contact Name:</th>
                                        <td colspan="3">
                                            <?php echo htmlspecialchars($ticket['contact_name'] ?? 'N/A'); ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Product Type :</th>
                                        <td colspan="3">
                                            <?php echo htmlspecialchars($ticket['producttype'] ?? 'N/A'); ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Login :</th>
                                        <td colspan="3"><?=$site_login_name?>
                                           
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <!-- Ticket Information Section -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Ticket Information</h5>
                                <table class="table table-sm">
                                    <tr>
                                        <th width="160">Ticket Type :</th>
                                        <td>
                                            <?php
                                            $ticket_types = [
                                                '1' => 'Incident',
                                                '2' => 'Problem',
                                                '3' => 'Change',
                                                '4' => 'Request',
                                                '5' => 'Service Request'
                                            ];
                                            echo htmlspecialchars($ticket_types[$ticket['ticket_type']] ?? 'N/A');
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Service Impact :</th>
                                        <td>
                                            <?php
                                            $impact_types = [
                                                '1' => 'กระทบ',
                                                '2' => 'ไม่กระทบ'
                                            ];
                                            echo htmlspecialchars($impact_types[$ticket['affecting_service']] ?? 'N/A');
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Symptoms :</th>
                                        <td>
                                            <?php
                                            echo htmlspecialchars($symptoms_list[$ticket['symptoms_details']] ?? 'N/A');
                                            ?>
                                        </td>
                                    </tr>
                                    <!--tr>
                                        <th>Product Type :</th>
                                        <td>
                                            <?php
                                            //echo htmlspecialchars($product_types_list[$ticket['product_type']] ?? 'N/A');
                                            ?>
                                        </td>
                                    </tr -->
                                    <tr>
                                        <th>Severity :</th>
                                        <td>
                                            <span class="badge badge-info">
                                                <?php
                                                $severity_types = [
                                                    'S1' => 'Severity 1 (S1- Service Critical Impact)',
                                                    'S2' => 'Severity 2 (S2- Significant Impact)',
                                                    'S3' => 'Severity 3 (S3- Minor impact)',
                                                    'S4' => 'Severity 4 (S4- Low Impact)'
                                                ];
                                                echo htmlspecialchars($severity_types[$ticket['severity']] ?? 'N/A');
                                                ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Priority :</th>
                                        <td>
                                            <span class="badge badge-<?php echo getPriorityBadgeClass($ticket['priority']); ?>">
                                                <?php echo htmlspecialchars($ticket['priority']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Current Status :</th>
                                        <td>
                                            <span class="badge badge-<?php echo getStatusBadgeClass($ticket['status']); ?>">
                                                <?php
                                                $status = $ticket['status'] ?? '';
                                                if ($status === 'Pending') {
                                                    echo 'On Hold(Stop Clock)';
                                                } else {
                                                    echo htmlspecialchars($status);
                                                }

                                                ?> <span class="badge badge-secondary"><?php echo htmlspecialchars($ticket['sub_status'] ?? ''); ?></span>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Assigned Staff:</th>
                                        <td><?php echo htmlspecialchars($ticket['assigned_worker_name'] ?? $ticket['assigned_worker'] ?? 'N/A'); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Created By :</th>
                                        <td><?php
                                            $username = $ticket['username'] ?? 'N/A';
                                            echo htmlspecialchars($ticket['created_by'] ?? $username);
                                            ?></td>
                                    </tr>
                                    <tr>
                                        <th>Created At :</th>
                                        <td><?php echo date('d/m/Y H :i', strtotime($ticket['created_at'])); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Open At :</th>
                                        <td><?php echo date('d/m/Y H :i', strtotime($ticket['open_at'])); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Updated At :</th>
                                        <td><?php echo date('d/m/Y H :i', strtotime($ticket['updated_at']));
                                            ?></td>
                                    </tr>
                                    <tr>
                                        <th>Close At :</th>
                                        <td><?php
                                            if ($ticket['closed_at']) {
                                                echo date('d/m/Y H :i', strtotime($ticket['closed_at']));
                                            } else {
                                                echo 'N/A';
                                            }
                                            ?></td>
                                    </tr>
                                    <tr>
                                        <th>Close By :</th>
                                        <td><?php echo htmlspecialchars($ticket['closed_by'] ?? 'N/A'); ?></td>
                                    </tr>

                                    <tr>
                                        <th>Working Time :</th>
                                        <td>
                                            <?php
                                            $working_time = calculateTicketWorkingTimeById($ticket['ticket_number']);
                                            if ($working_time) {
                                                echo '<span class="badge badge-info">' . $working_time['formatted'] . '</span>';
                                                if ($ticket['status'] == 'Pending') {
                                                    echo ' <small class="text-muted">(Paused)</small>';
                                                } elseif ($ticket['status'] !== 'Closed') {
                                                    echo ' <small class="text-muted">(Running)</small>';
                                                }
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Channel Type :</th>
                                        <td>
                                            <?php
                                            // Display channel type from database, with fallback to hardcoded values if not found
                                            if (isset($channel_types_list[$ticket['channel_type']])) {
                                                echo htmlspecialchars($channel_types_list[$ticket['channel_type']]);
                                            } else {
                                                echo 'Unknown';
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <div class="mt-4">
                            <div class="d-flex align-items-center">
                                <h5 class="text-<?= getStatusBadgeClass($ticket['status']) ?> mb-0">
                                    <i class="fas fa-exclamation-circle"></i> Issue Details
                                </h5>
                                <?php
                                //check interim status
                                if ($ticket['have_interim'] === 1) {
                                    echo '<span class="badge badge-warning ml-2 interim" title="Interim is active"><i class="fas fa-bell"></i> Interim Active</span>';
                                }
                                ?>
                            </div>
                            <div class="p-3 rounded issue-details-box">
                                <?php echo nl2br(htmlspecialchars($ticket['issue_details'])); ?>
                            </div>
                        </div>

                        <!-- Update Status Form -->
                        <div class="mt-4 small">
                            <h5>Update Status</h5>
                            <form method="POST" class="form-horizontal">
                                <input type="hidden" name="ticket_number" value="<?php echo htmlspecialchars($ticket['ticket_number']); ?>">
                                <input type="hidden" name="current_status" value="<?php echo htmlspecialchars($ticket['status']); ?>">
                                <!-- Always submit sub_status, even if not visible -->
                                <input type="hidden" name="sub_status" id="hidden-sub-status" value="">
                                <input type="hidden" name="action" value="update_status">
                                <input type="hidden" id="cause_detail_template_id" name="cause_detail_template_id" value="<?= $ticket['cause_detail_template_id'] ?? 0 ?>">
                                <input type="hidden" name="cause_name_en_email" value="<?= $cause_detail_en ?>">

                                <?php if ($ticket['status'] === 'Closed') : ?>
                                    <div class="alert alert-warning">
                                        <i class="fas fa-lock"></i> This ticket is closed and cannot be modified.
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-12">
                                            <h5>Causes Of Failure</h5>
                                            <div class="form-group row">

                                                <label class="col-sm-2 col-form-label">Cause Description:</label>
                                                <div class="col-sm-6">
                                                    <?= $cause_detail ?>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-sm-2 col-form-label">Cause Description(en) :</label>
                                                <div class="col-sm-6">
                                                    <?= $cause_detail_en ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php else :
                                    $fault_cleared = $_GET['fault_cleared'] ?? '';
                                    $uptime = urldecode($_GET['uptime'] ?? date('Y-m-d H:i:s'));
                                    $pre_comment = '';
                                    if ($fault_cleared == '1') {
                                        echo '<input type="hidden" name="fault_cleared" value="1">';
                                        $pre_comment = "Monitoring Fault cleared at (UpTime): " . date('d/m/Y H:i', strtotime($uptime)) . ". ";
                                    }
                                    //var_dump($uptime, $fault_cleared, $pre_comment);

                                ?>
                                    <div class="form-group row">
                                        <label class="col-sm-2 col-form-label">New Status :</label>
                                        <div class="col-sm-4">
                                            <select name="status" id="status-select" class="form-control" required <?php echo ($ticket['status'] === 'Closed' ? 'disabled'  : ''); ?>>
                                                <option value="">Select Status</option>
                                                <?php if ($fault_cleared == '1') : ?>
                                                    <option value="Closed" selected>Closed</option>
                                                <?php else : ?>
                                                    <option value="Open" <?php echo ($ticket['status'] == 'Open') ? 'selected'  : ''; ?>>Open</option>
                                                    <option value="In Progress" <?php echo ($ticket['status'] == 'In Progress') ? 'selected'  : ''; ?>>In Progress</option>
                                                    <option value="Pending" <?php echo ($ticket['status'] == 'Pending') ? 'selected'  : ''; ?>>On Hold(Stop Clock)</option>
                                                    <option value="Closed" <?php echo ($ticket['status'] == 'Closed') ? 'selected'  : ''; ?>>Closed</option>
                                                <?php endif; ?>
                                            </select>

                                        </div>
                                        <div class="col-sm-2 mt-2" id="send-email-checkbox" style="display: none;">
                                            <input type="checkbox" name="send_email" id="send_email" value="1">
                                            <label for="send_email">Send Email Notify To Customer</label>
                                        </div>
                                    </div>

                                    <!-- Sub-status options that appear based on selected status -->
                                    <div class="form-group row sub-status-section" id="in-progress-options" style="display: none;">
                                        <label class="col-sm-2 col-form-label">Status Detail :</label>
                                        <div class="col-sm-4">
                                            <select name="sub_status_options" class="form-control">
                                                <option value="">Select Detail</option>
                                                <option value="Wait Customer support" <?= ($ticket['sub_status'] == 'Wait Customer support') ? 'selected' : '' ?>>Wait Customer support</option>
                                                <option value="Wait Implement" <?= ($ticket['sub_status'] == 'Wait Implement') ? 'selected' : '' ?>>Wait Implement</option>
                                                <option value="Wait PM" <?= ($ticket['sub_status'] == 'Wait PM') ? 'selected' : '' ?>>Wait PM</option>
                                                <option value="Wait Operator" <?= ($ticket['sub_status'] == 'Wait Operator') ? 'selected' : '' ?>>Wait Operator</option>
                                            </select>
                                        </div>

                                    </div>

                                    <div class="form-group row sub-status-section" id="pending-options" style="display: none;">
                                        <label class="col-sm-2 col-form-label">Status Detail :</label>
                                        <div class="col-sm-4">
                                            <select name="sub_status_options" class="form-control">
                                                <option value="">Select Detail</option>
                                                <option value="Wait Customer">Wait Customer</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row sub-status-section" id="close-options" style="display: none;">
                                        <div class="col-12">
                                            <div class="form-group row">
                                                <label class="col-sm-2 col-form-label">Close At :</label>
                                                <div class="col-sm-4">
                                                    <input type="datetime-local" name="closed_at" id="closed_at" class="form-control"
                                                        value="<?php
                                                                if ($fault_cleared == '1') {
                                                                    echo date('Y-m-d H:i:s', strtotime($uptime));
                                                                } else {
                                                                    echo date('Y-m-d H:i:s', strtotime($ticket['closed_at'] ?? 'now'));
                                                                } ?>" <?php echo ($ticket['status'] === 'Closed' ? 'disabled'  : ''); ?>>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="col-12">
                                                    <h5>Causes Of Failure</h5>

                                                    <div class="form-group row">

                                                        <label class="col-sm-2 col-form-label">Cause Description:</label>
                                                        <div class="col-sm-6">
                                                            <?= $cause_detail ?>
                                                            <select name="cause_name" id="cause_name" class="form-control select2" required>
                                                                <option value="">Search Cause Description...Or press % Show all</option>
                                                                <?php if ($cause_detail !== ''): ?>
                                                                    <option value="<?= $cause_detail ?>" selected><?= $cause_detail ?></option>
                                                                <?php endif; ?>
                                                            </select>

                                                        </div>
                                                    </div>
                                                    <div class="form-group row">
                                                        <label class="col-sm-2 col-form-label">Cause Description(en) :</label>
                                                        <div class="col-sm-6">
                                                            <textarea name="cause_name_en" id="cause_name_en" placeholder="Cause Description(english)" class="form-control" rows="3" placeholder="" readonly><?= $cause_detail_en ?></textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> <!-- end close options -->
                                    <div class="form-group row sub-status-section" id="open-options" style="display: none;">
                                        <label class="col-sm-2 col-form-label">Open At :</label>
                                        <div class="col-sm-4">
                                            <input type="datetime-local" name="open_at" id="open_at" class="form-control" value="<?php echo date('Y-m-d H:i:s', strtotime($ticket['open_at'] ?? 'now')); ?>" <?php echo ($ticket['status'] === 'Closed' ? 'disabled'  : ''); ?>>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-2 col-form-label">Comments :</label>
                                        <div class="col-sm-6">
                                            <textarea name="status_comment" class="form-control" rows="2"
                                                placeholder="Add any comments about this status change..." <?php echo ($ticket['status'] === 'Closed' ? 'disabled'  : ''); ?>><?= ($fault_cleared == '1') ? $pre_comment : ''; ?></textarea>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <div class="col-sm-10 offset-sm-2">
                                            <button type="submit" class="btn btn-primary" <?php echo ($ticket['status'] === 'Closed' ? 'disabled'  : ''); ?>>
                                                <i class="fas fa-save"></i> Update Status
                                            </button>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </form>

                        </div>

                        <!-- Status History -->
                        <div class="mt-4 small text-secondary">
                            <h5>Status History</h5>
                            <?php

                            $history_query = "SELECT  tsh.*, u.username,
                                        (SELECT tm.`name` FROM 
                                    team_members tmm LEFT JOIN teams tm ON tmm.team_id = tm.id
                                    WHERE tmm.user_id=tsh.changed_by LIMIT 1) AS team
                                    FROM  ticket_status_history tsh LEFT JOIN users u ON tsh.changed_by = u.id
                                    WHERE tsh.ticket_number = ?
                                    ORDER BY tsh.changed_at DESC";

                            $history_stmt = $pdo->prepare($history_query);
                            $history_stmt->execute([$ticket_id]);
                            $status_history = $history_stmt->fetchAll(PDO::FETCH_ASSOC);
                            ?>

                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date/Time</th>
                                            <th>Status</th>
                                            <th>Changed By</th>
                                            <th>Comments</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($status_history as $history) : ?>
                                            <tr>
                                                <td><?php echo date('d/m/Y H :i', strtotime($history['changed_at'])); ?></td>
                                                <td>
                                                    <span class="badge badge-<?php echo getStatusBadgeClass($history['status']); ?>">
                                                        <?php
                                                        $status = $history['status'] ?? '';
                                                        if ($status === 'Pending') {
                                                            echo 'On Hold(Stop Clock)';
                                                        } else {
                                                            echo htmlspecialchars($status);
                                                        }
                                                        ?> <span class="badge badge-secondary"> <?php echo htmlspecialchars($history['sub_status'] ?? ''); ?></span>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($history['username']); ?>
                                                    <span class="badge badge-secondary"><?php echo htmlspecialchars($history['team'] ?? ''); ?></span>
                                                </td>
                                                <td><?php echo nl2br(htmlspecialchars($history['comments'])); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                        <?php if (empty($status_history)) : ?>
                                            <tr>
                                                <td colspan="4" class="text-center text-muted">No status changes recorded</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Forwarding History 1 -->
                        <div class="mt-4 small text-secondary">
                            <h5>Forwarding/Transferring History</h5>
                            <?php

                            $forwards_query = "SELECT tf.*,u.username AS forwarded_by_name
                            FROM  ticket_forwards tf LEFT JOIN users u ON tf.forwarded_by = u.id
                            WHERE tf.ticket_number = ?
                            ORDER BY
                                tf.created_at DESC";

                            $forwards_stmt = $pdo->prepare($forwards_query);
                            $forwards_stmt->execute([$ticket_id]);
                            $forwards_history = $forwards_stmt->fetchAll(PDO::FETCH_ASSOC);
                            ?>

                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date/Time</th>
                                            <th>From</th>
                                            <th>To</th>
                                            <th>Forwarded By</th>
                                            <th>Notes</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($forwards_history as $forward) : ?>
                                            <tr>
                                                <td><?php echo date('d/m/Y H :i', strtotime($forward['created_at'])); ?></td>
                                                <td><?php echo htmlspecialchars($forward['from_worker']??''); ?></td>
                                                <td><?php echo htmlspecialchars($forward['to_worker']??''); ?></td>
                                                <td><?php echo htmlspecialchars($forward['forwarded_by_name']??''); ?></td>
                                                <td><?php echo nl2br(htmlspecialchars($forward['notes']??'')); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                        <?php if (empty($forwards_history)) : ?>
                                            <tr>
                                                <td colspan="5" class="text-center text-muted">No forwarding history</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!--Section with Tabs Comments/Forward/Interim-->
                        <div class="mt-4">
                            <div class="card">
                                <div class="card-header">
                                    <ul class="nav nav-tabs card-header-tabs" id="ticketActionTabs" role="tablist">
                                        <li class="nav-item">
                                            <a class="nav-link active" id="comments-tab" data-toggle="tab" href="#comments-content" role="tab" aria-controls="comments-content" aria-selected="true">
                                                <i class="fas fa-comments"></i> Comments
                                            </a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="forward-tab" data-toggle="tab" href="#forward-content" role="tab" aria-controls="forward-content" aria-selected="false">
                                                <i class="fas fa-exchange-alt"></i> Forward/Escalate
                                            </a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="interim-tab" data-toggle="tab" href="#interim-content" role="tab" aria-controls="interim-content" aria-selected="false">
                                                <i class="fas fa-clipboard-list"></i> Interim
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="card-body">
                                    <div class="tab-content" id="ticketActionTabsContent">
                                        <!-- Comments Tab -->
                                        <div class="tab-pane fade show active" id="comments-content" role="tabpanel" aria-labelledby="comments-tab">
                                            <!-- Comment Form -->
                                            <?php if ($ticket['status'] !== 'Closed'): ?>
                                                <form id="editorForm" method="POST" enctype="multipart/form-data">
                                                    <div class="mb-3">
                                                        <div id="editorComment"></div>
                                                        <textarea id="hiddenComment" name="comment" style="display :none;"></textarea>
                                                    </div>
                                                    <div class="mb-3">
                                                        <button type="submit" class="btn btn-primary">
                                                            <i class="fas fa-comment"></i> Add Comment
                                                        </button>
                                                    </div>
                                                </form>
                                            <?php endif; ?>
                                            <!-- Comments List -->
                                            <div class="comments-section mt-4">
                                                <h5>Comment History</h5>
                                                <?php if (!empty($comments)) : ?>
                                                    <?php foreach ($comments as $comment) : ?>
                                                        <div class="comment card mb-3">
                                                            <div class="card-body">
                                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                                    <div>
                                                                        <strong class="text-primary">
                                                                            <i class="fas fa-user-circle"></i>
                                                                            <?php echo htmlspecialchars($comment['username']); ?>
                                                                        </strong>
                                                                        <span class="badge badge-secondary ml-2">
                                                                            <?php echo htmlspecialchars($comment['team'] ?? ''); ?>
                                                                        </span>
                                                                    </div>
                                                                    <small class="text-muted">
                                                                        <i class="fas fa-clock"></i>
                                                                        <?php echo date('d/m/Y H :i', strtotime($comment['created_at'])); ?>
                                                                    </small>
                                                                </div>
                                                                <div class="comment-content">
                                                                    <?php echo $comment['comment'] ?? base64_decode($comment['comment_base64']); // Display HTML content 
                                                                    ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; ?>
                                                <?php else : ?>
                                                    <div class="alert alert-info">
                                                        No comments yet. Be the first to comment!
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                        </div>

                                        <!-- Forward Ticket Tab -->
                                        <div class="tab-pane fade" id="forward-content" role="tabpanel" aria-labelledby="forward-tab">
                                            <?php if ($ticket['status'] !== 'Closed'): ?>
                                                <form method="POST" id="forwardForm">
                                                    <div class="form-row">
                                                        <div class="form-group col-md-6">
                                                            <label for="forward_worker">Forward to Staff:</label>
                                                            <select name="forward_worker" id="forward_worker" class="form-control" required>
                                                                <option value="">Select Staff</option>
                                                                <?php
                                                                foreach ($workers as $worker) :
                                                                    // Don't show current worker in the dropdown
                                                                    if ($worker['username'] != $ticket['assigned_worker']) :
                                                                ?>
                                                                        <option value="<?php echo $worker['username']; ?>">
                                                                            <?php echo htmlspecialchars($worker['fullname']); ?>
                                                                        </option>
                                                                <?php
                                                                    endif;
                                                                endforeach;
                                                                ?>
                                                            </select>
                                                        </div>
                                                        <div class="form-group col-md-6">
                                                            <label for="forward_priority">Priority :</label>
                                                            <select name="forward_priority" id="forward_priority" class="form-control">
                                                                <option value="<?php echo htmlspecialchars($ticket['priority']); ?>">
                                                                    Keep Current (<?php echo htmlspecialchars($ticket['priority']); ?>)
                                                                </option>
                                                                <option value="High">High</option>
                                                                <option value="Medium">Medium</option>
                                                                <option value="Low">Low</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="forward_notes">Notes for Team :</label>
                                                        <textarea name="forward_notes" id="forward_notes" class="form-control" rows="3"
                                                            placeholder="Explain why you're forwarding this ticket and what needs to be done next..." required></textarea>
                                                    </div>
                                                    <input type="hidden" name="action" value="forward_ticket">
                                                    <input type="hidden" name="ticket_number" value="<?php echo htmlspecialchars($ticket['ticket_number']); ?>">
                                                    <button type="submit" class="btn btn-info" <?php echo ($ticket['status'] === 'Closed' ? 'disabled'  : ''); ?>>
                                                        <i class="fas fa-paper-plane"></i> Forward Ticket
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                           
                                        </div>

                                        <!-- Interim Tab -->
                                        <div class="tab-pane fade" id="interim-content" role="tabpanel" aria-labelledby="interim-tab">
                                            <?php
                                            // Fetch existing interim data if available

                                            ?>

                                            <?php if ($ticket['status'] !== 'Closed'): ?>
                                                <form method="POST" id="interimForm">
                                                    <input type="hidden" name="action" value="update_interim">

                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label for="mailto"><i class="fas fa-envelope"></i> Mail To : <span class="text-danger">*</span></label>
                                                                <input type="email"
                                                                    class="form-control"
                                                                    id="mailto"
                                                                    name="mailto"
                                                                    multiple
                                                                    value="<?php echo htmlspecialchars($interim_data['mailto'] ?? $interim_mailto); ?>"
                                                                    required>
                                                            </div>

                                                            <div class="form-group">
                                                                <label for="login_temp"><i class="fas fa-key"></i> Login Temp : <span class="text-danger">*</span></label>
                                                                <input type="text" class="form-control" id="login_temp" name="login_temp"
                                                                    value="<?php echo htmlspecialchars($interim_data['login_temp'] ?? ''); ?>" required>
                                                            </div>

                                                            <div class="form-group">
                                                                <label for="sim_no"><i class="fas fa-sim-card"></i> SIM Number : <span class="text-danger">*</span></label>
                                                                <input type="text" class="form-control" id="sim_no" name="sim_no"
                                                                    value="<?php echo htmlspecialchars($interim_data['sim_no'] ?? ''); ?>" required>
                                                            </div>
                                                        </div>

                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label for="sim_serial"><i class="fas fa-barcode"></i> SIM Serial Number : <span class="text-danger">*</span></label>
                                                                <input type="text" class="form-control" id="sim_serial" name="sim_serial"
                                                                    value="<?php echo htmlspecialchars($interim_data['sim_serial'] ?? ''); ?>" required>
                                                            </div>

                                                            <div class="form-group">
                                                                <label for="sim_operator"><i class="fas fa-broadcast-tower"></i> SIM Operator : <span class="text-danger">*</span></label>
                                                                <select class="form-control" id="sim_operator" name="sim_operator" required>
                                                                    <option value="">-- Select Operator --</option>
                                                                    <?php
                                                                    $operators = ['AIS', 'DTAC', 'NT', 'TRUE', 'Other'];
                                                                    foreach ($operators as $operator) {
                                                                        $selected = ($interim_data['sim_operator'] ?? '') === $operator ? 'selected'  : '';
                                                                        echo "<option value=\"$operator\" $selected>$operator</option>";
                                                                    }
                                                                    ?>
                                                                </select>
                                                            </div>

                                                            <div class="form-group">
                                                                <label for="sim_package"><i class="fas fa-box"></i> SIM Package : <span class="text-danger">*</span></label>
                                                                <select class="form-control" id="sim_package" name="sim_package" required>
                                                                    <option value="">-- Select Package --</option>
                                                                    <?php
                                                                    $packages = ['3G_AWifi 1ToAll M2MBusiness199BFixIPCorp', '3G_AWifi 1ToAll M2MBusiness349BFixIPCorp', '3G_AWifi CorpAPN M2M Pay 599BFixIPCorp', '3G_Corp APN Business Pay299BFixIPCorp', 'Other'];
                                                                    foreach ($packages as $package) {
                                                                        $selected = ($interim_data['sim_package'] ?? '') === $package ? 'selected'  : '';
                                                                        echo "<option value=\"$package\" $selected>$package</option>";
                                                                    }
                                                                    ?>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="form-group mt-3">
                                                        <button type="submit" class="btn btn-primary" <?php echo ($ticket['status'] === 'Closed' ? 'disabled'  : ''); ?>>
                                                            <i class="fas fa-bell"></i> <?= $button_text_interim ?>
                                                        </button>
                                                    </div>
                                                    <input type="hidden" id="request_interim" name="request_interim" value="<?= $request_interim ?>">
                                                </form>
                                            <?php endif; ?>

                                            <div class="mt-4">
                                                <div class="card bg-light">
                                                    <div class="card-header">
                                                        <h5>Interim Information History</h5>
                                                    </div>
                                                    <?php if ($interim_data) : ?>
                                                        <div class="card-body small">
                                                            <div class="row">
                                                                <div class="col-md-6">
                                                                    <p><strong>Created By :</strong>
                                                                        <?php
                                                                        $creator_query = "SELECT username FROM users WHERE id = ?";
                                                                        $creator_stmt = $pdo->prepare($creator_query);
                                                                        $creator_stmt->execute([$interim_data['created_by']]);
                                                                        echo htmlspecialchars($creator_stmt->fetchColumn() ?: 'Unknown');
                                                                        ?>
                                                                    </p>
                                                                    <p><strong>Created At :</strong>
                                                                        <?php echo date('d/m/Y H :i', strtotime($interim_data['created_at'])); ?>
                                                                    </p>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <?php if (!empty($interim_data['updated_by'])) : ?>
                                                                        <p><strong>Last Updated By :</strong>
                                                                            <?php
                                                                            $updater_query = "SELECT username FROM users WHERE id = ?";
                                                                            $updater_stmt = $pdo->prepare($updater_query);
                                                                            $updater_stmt->execute([$interim_data['updated_by']]);
                                                                            echo htmlspecialchars($updater_stmt->fetchColumn() ?: 'Unknown');
                                                                            ?>
                                                                        </p>
                                                                        <p><strong>Last Updated At :</strong>
                                                                            <?php echo date('d/m/Y H :i', strtotime($interim_data['updated_at'])); ?>
                                                                        </p>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>

                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="card-footer text-right">
                        <a href="list.php" class="btn btn-secondary">Back to List</a>
                        <a href="edit.php?tkt_id=<?php echo $ticket_id; ?>" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Ticket
                        </a>
                    </div>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Floating Help Note -->
<div id="helpNote" class="position-fixed shadow-sm" style="width : 300px; z-index : 1000; right : 20px; top : 80px;">
    <div class="card note-card">
        <div class="card-header note-header py-2" id="helpDragHandle">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> Help Guide
                </h6>
                <div>
                    <button type="button" class="btn btn-link btn-sm text-secondary p-0 mr-2" id="minimizeHelp">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="btn btn-link btn-sm text-secondary p-0" id="closeHelp">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body note-body py-2" id="helpContent">
            <div class="help-item mb-2">
                <strong><i class="fas fa-ticket-alt text-primary"></i> Status Flow :</strong>
                <ul class="small pl-4 mb-0 mt-1">
                    <li>Open → In Progress → Closed</li>
                    <li>Use On Hold(Stop Clock) when waiting for response</li>
                    <li>Closed tickets cannot be reopened</li>
                </ul>
            </div>
            <div class="help-item mb-2">
                <strong><i class="fas fa-clock text-warning"></i> Working Time :</strong>
                <ul class="small pl-4 mb-0 mt-1">
                    <li>Starts when ticket is created</li>
                    <li>Pauses during On Hold(Stop Clock) status</li>
                    <li>Stops when status is Closed</li>
                </ul>
            </div>
            <div class="help-item">
                <strong><i class="fas fa-comments text-success"></i> Comments :</strong>
                <ul class="small pl-4 mb-0 mt-1">
                    <li>Add updates about progress</li>
                    <li>Include relevant details</li>
                    <li>Mention next actions</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="extendedInfoModal" tabindex="-1" role="dialog" aria-labelledby="extendedInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="extendedInfoModalLabel">Extended Ticket Information</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <?php


                if ($extended_info) :
                ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-sm">
                            <tbody>
                                <tr>
                                    <th width="200">Contact Person</th>
                                    <td><?php echo htmlspecialchars($extended_info['contact_person'] ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Location</th>
                                    <td><?php echo htmlspecialchars($extended_info['location'] ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Effect</th>
                                    <td><?php echo htmlspecialchars($extended_info['effect'] ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Project Name</th>
                                    <td><?php echo htmlspecialchars($extended_info['project_name'] ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Owner</th>
                                    <td><?php echo htmlspecialchars($extended_info['owner'] ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>NOC Receiver</th>
                                    <td><?php echo htmlspecialchars($extended_info['noc_receiver'] ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>NOC Closer</th>
                                    <td><?php echo htmlspecialchars($extended_info['noc_closer'] ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Main Cause</th>
                                    <td><?php echo htmlspecialchars($extended_info['main_cause'] ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Sub Cause</th>
                                    <td><?php echo htmlspecialchars($extended_info['sub_cause'] ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Group Cause 1</th>
                                    <td><?php echo htmlspecialchars($extended_info['group_cause1'] ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Group Cause 2</th>
                                    <td><?php echo htmlspecialchars($extended_info['group_cause2'] ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Cause Detail</th>
                                    <td><?php echo htmlspecialchars($extended_info['cause_detail'] ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Cause Detail (English)</th>
                                    <td><?php echo htmlspecialchars($extended_info['cause_detail_eng'] ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Provider</th>
                                    <td><?php echo htmlspecialchars($extended_info['provider'] ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Province</th>
                                    <td><?php echo htmlspecialchars($extended_info['province'] ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Downtime (Hours)</th>
                                    <td><?php echo htmlspecialchars($extended_info['downtime_hr'] ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Downtime (Minutes)</th>
                                    <td><?php echo htmlspecialchars($extended_info['downtime_min'] ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Total Time</th>
                                    <td><?php echo htmlspecialchars($extended_info['total_time'] ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Stop Clock</th>
                                    <td><?php echo htmlspecialchars($extended_info['stop_clock'] ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Category</th>
                                    <td><?php echo htmlspecialchars($extended_info['category'] ?? 'N/A'); ?></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                <?php else : ?>
                    <div class="alert alert-info">
                        No extended information available for this ticket.
                    </div>
                <?php endif; ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer and scripts
// This is where you would include any additional scripts or stylesheets
require_once '../../includes/main_script_loader.php';
?>
<!-- Add these in the header or before closing </body> -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap4-theme@1.0.0/dist/select2-bootstrap4.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!--Include the JS & CSS-->
<link rel="stylesheet" href="<?= BASE_URL ?>/assets/plugins/MyWYSIWYG/css/rich-text-editor.min.css" />
<script type="text/javascript" src='<?= BASE_URL ?>/assets/plugins/MyWYSIWYG/js/rich-text-editor.min.js'></script>
<script src="../../assets/js/ticket-view.js?v=<?= $timestamp ?>"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var editor = null;
        const statusSelect = document.getElementById('status-select');
        const inProgressOptions = document.getElementById('in-progress-options');
        const pendingOptions = document.getElementById('pending-options');
        const closeOptions = document.getElementById('close-options');
        const openOptions = document.getElementById('open-options');
        const send_email_checkbox = document.getElementById('send-email-checkbox');
        const causeNameSelect = document.getElementById('cause_name');
        const requestInterim = document.getElementById('request_interim');

        if (document.getElementById('editorComment') !== null) {
            editor = new RichTextEditor('#editorComment');
            document.getElementById('editorForm').addEventListener('submit', (e) => {
                document.getElementById('hiddenComment').value = editor.getContent();
            });
        }

        if (statusSelect !== null) {
            // Initial check on page load
            updateSubStatusVisibility();
            // Add event listener for status changes
            statusSelect.addEventListener('change', updateSubStatusVisibility);
        } else {
            console.log('Status select element not found.');
        }

        // On form submit, ensure hidden-sub-status is up to date
        document.querySelector('form[method="POST"]').addEventListener('submit', function() {
            let subStatus = '';
            if (statusSelect.value === 'In Progress') {
                subStatus = inProgressOptions.querySelector('select[name="sub_status_options"]').value;
            } else if (statusSelect.value === 'Pending') {
                subStatus = pendingOptions.querySelector('select[name="sub_status_options"]').value;
            }
            document.getElementById('hidden-sub-status').value = subStatus;
        });

        if (document.getElementById('cause_name') !== null) {
            document.getElementById('cause_name').addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                const causeEn = selectedOption.getAttribute('data-cause_en') || '';
                document.getElementById('cause_name_en').value = causeEn;
                document.querySelector('input[name="cause_name_en_email"]').value = causeEn;
                document.getElementById('cause_detail_template_id').value = selectedOption.getAttribute('data-template_id') || '';

            });
        }

        function updateSubStatusVisibility() {
            // Hide all sub-status sections first
            document.querySelectorAll('.sub-status-section').forEach(el => {
                el.style.display = 'none';
            });
            send_email_checkbox.style.display = 'none'; // Hide email checkbox by default

            // Reset hidden sub_status value
            document.getElementById('hidden-sub-status').value = '';

            // Show relevant section based on selected status
            const selectedStatus = statusSelect.value;
            if (requestInterim && requestInterim.value === '0' && selectedStatus === 'Closed') {
                alert('Please uninstall interim before closing the ticket !!!.');
                statusSelect.value = ''; // Reset selection
                return false;
            }
            if (selectedStatus === 'In Progress') {
                inProgressOptions.style.display = 'flex';
                send_email_checkbox.style.display = 'block'; // Show email checkbox for In Progress
                // Hide email checkbox for Pending
                // Set hidden value from dropdown
                const select = inProgressOptions.querySelector('select[name="sub_status_options"]');
                document.getElementById('hidden-sub-status').value = select.value;
                select.addEventListener('change', function() {
                    document.getElementById('hidden-sub-status').value = this.value;
                });
            } else if (selectedStatus === 'Pending') {
                pendingOptions.style.display = 'flex';
                const select = pendingOptions.querySelector('select[name="sub_status_options"]');
                document.getElementById('hidden-sub-status').value = select.value;
                select.addEventListener('change', function() {
                    document.getElementById('hidden-sub-status').value = this.value;
                });
            } else if (selectedStatus === 'Closed') {
                send_email_checkbox.style.display = 'block';
                closeOptions.style.display = 'flex';
                document.getElementById('hidden-sub-status').value = '';
                fetchCauseDescription();
            } else if (selectedStatus === 'Open') {
                openOptions.style.display = 'flex';
                // Set hidden value for Open status
                document.getElementById('hidden-sub-status').value = '';
            } else {
                // If no valid status is selected, hide all sub-status sections
                inProgressOptions.style.display = 'none';
                pendingOptions.style.display = 'none';
                closeOptions.style.display = 'none';
                openOptions.style.display = 'none';
            }
            if (statusSelect && causeNameSelect) {
                if (statusSelect.value === 'Closed') {
                    causeNameSelect.setAttribute('required', 'required');
                } else {
                    causeNameSelect.removeAttribute('required');
                }
            }
        } //function updateSubStatusVisibility

    });
</script>

<script>
    $(document).ready(function() {
        //send_email onclick
        $('#send_email').on('change', function() {
            if ($(this).is(':checked')) {
                if ($('#cause_name_en').val() === '' && $('#cause_name_en').val() === 'Closed') {
                    alert('Please select a `Cause Description`! and click `Update Cause`.');
                    $(this).prop('checked', false); // Uncheck the checkbox
                }
            }
        });

        // Initialize Select2
        $('#cause_name').select2({
            theme: 'bootstrap4',
            placeholder: 'Search Cause Description...Or press % Show all',
            allowClear: true,
            width: '100%',
            ajax: {
                url: 'ajax_cause_template.php',
                dataType: 'json',
                delay: 250,
                data: function(params) {
                    return {
                        search: params.term, // search term
                        page: params.page
                    };
                },
                processResults: function(data, params) {
                    //console.log(data);

                    params.page = params.page || 1;

                    return {
                        results: data.map(item => ({
                            id: item.cause_name,
                            text: item.cause_name,
                            cause_en: item.cause_name_en,
                            template_id: item.id
                        })),
                        pagination: {
                            more: false
                        }
                    };
                },
                cache: true
            },
            minimumInputLength: 1
        }).on('select2:select', function(e) {
            const data = e.params.data;
            document.getElementById('cause_name_en').value = data.cause_en;
            document.querySelector('input[name="cause_name_en_email"]').value = data.cause_en;
            document.getElementById('cause_detail_template_id').value = data.template_id;
        });
    });


    function fetchCauseDescription() {
        let params = new URLSearchParams();

        fetch('ajax_cause_template.php?' + params)
            .then(res => res.json())
            .then(data => {

                const causeNameSelect = document.getElementById('cause_name');
                causeNameSelect.innerHTML = '<option value="">Select Detail</option>';
                if (Array.isArray(data) && data.length > 0) {

                    data.forEach(item => {
                        if (item.cause_name) {
                            causeNameSelect.innerHTML += `<option data-template_id="${item.id}" data-cause_en="${item.cause_name_en}" value="${item.cause_name}">${item.cause_name}</option>`;
                        }
                    });
                }
            });
    }
</script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const interimForm = document.getElementById('interimForm');
        const requestInterim = document.getElementById('request_interim');

        if (interimForm) {
            interimForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                let confirmMessage = 'Are you sure you want to ->';
                confirmMessage += (requestInterim.value === '1') ? 'Request' : 'Uninstall';
                confirmMessage += ' interim?';

                if (confirm(confirmMessage)) {
                    // If confirmed, show processing message
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

                    // Submit the form
                    this.submit();
                }
            });
        }
    });
</script>
<?php
require_once '../../includes/footer.php';
?>