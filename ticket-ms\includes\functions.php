<?php
if (!defined('ROOT_PATH')) {
    define("ROOT_PATH", '/var/www/html/cs.1-to-all.com/app');
}
require_once ROOT_PATH.'/ticket-ms/vendor/autoload.php';
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\Exception;

function text_to_readmore($text, $limit = 50) {
   
    $txt_readmore = mb_substr($text, 0, $limit, 'UTF-8'). '...';
    return $txt_readmore;
}
function getUserByUsername($username) {
    global $pdo;
    
    $query = "SELECT * FROM users WHERE username = :username";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':username', $username);
    $stmt->execute();
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}
function getUserSettings($userId) {
    global $pdo;
    
    $query = "SELECT * FROM user_settings WHERE user_id = :user_id";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':user_id', $userId);
    $stmt->execute();
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function updateUserSettings($userId, $settings) {
    global $pdo;
    
    // Check if settings exist for user
    $checkQuery = "SELECT COUNT(*) FROM user_settings WHERE user_id = :user_id";
    $checkStmt = $pdo->prepare($checkQuery);
    $checkStmt->bindParam(':user_id', $userId);
    $checkStmt->execute();
    
    if ($checkStmt->fetchColumn() > 0) {
        // Update existing settings
        $query = "UPDATE user_settings SET 
            email_notifications = :email_notifications,
            sms_notifications = :sms_notifications,
            system_notifications = :system_notifications,
            theme = :theme,
            language = :language,
            items_per_page = :items_per_page,
            updated_at = CURRENT_TIMESTAMP
            WHERE user_id = :user_id";
    } else {
        // Insert new settings
        $query = "INSERT INTO user_settings 
            (user_id, email_notifications, sms_notifications, system_notifications, 
             theme, language, items_per_page) 
            VALUES 
            (:user_id, :email_notifications, :sms_notifications, :system_notifications,
             :theme, :language, :items_per_page)";
    }
    
    try {
        $stmt = $pdo->prepare($query);
        
        // Bind all parameters
        $stmt->bindParam(':user_id', $userId);
        $stmt->bindParam(':email_notifications', $settings['email_notifications']);
        $stmt->bindParam(':sms_notifications', $settings['sms_notifications']);
        $stmt->bindParam(':system_notifications', $settings['system_notifications']);
        $stmt->bindParam(':theme', $settings['theme']);
        $stmt->bindParam(':language', $settings['language']);
        $stmt->bindParam(':items_per_page', $settings['items_per_page'], PDO::PARAM_INT);
        
        return $stmt->execute();
    } catch (PDOException $e) {
        error_log("Error updating user settings: " . $e->getMessage());
        throw $e;
    }
}

function getRecentTickets($limit = 10) {
    global $pdo;

    $query = "SELECT tk.ticket_number,tk.customer_number,cus.CusName AS customer_name
,tk.issue_details,tk.priority
,tk.`status`,tm.`name` AS assigne_team,tk.created_at
 FROM 
 tickets tk LEFT JOIN KCS_DB.Customers cus ON tk.customer_number=cus.CusCode
 LEFT JOIN teams tm ON tk.assigned_team = tm.id
 ORDER BY tk.created_at DESC LiMIT :limit"; 

    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function countTicketsByStatus($status, $year = null) {
    global $pdo;

    if ($year) {
        // Return total count by status and year
        $query = "SELECT COUNT(*) FROM tickets
                  WHERE status = :status
                    AND YEAR(created_at) = :year";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':year', $year);
        $stmt->execute();

        return $stmt->fetchColumn();
    } else {
        // Original functionality - just count by status
        $query = "SELECT COUNT(*) FROM tickets WHERE status = :status";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->execute();

        return $stmt->fetchColumn();
    }
}

function getTicketPriorities() {
    return ['High', 'Medium', 'Low'];
}

function logActivity($activity) {
    // Code to log activities related to ticket management
}

function sendNotification($userId, $message) {
    // Code to send notifications to users
}
function getTicketStatus() {
    return ['Open', 'In Progress', 'Pending', 'Closed'];
}
function getTicketTypes() {
    return ['Incident', 'Service Request', 'Change Request'];
}
function sendEmail($to, $subject, $message) {
    // Code to send email notifications
    return true; // Assuming email sent successfully
}
function getTicketDetails($ticketId) {
    global $pdo;
    
    $query = "SELECT * FROM tickets WHERE id = :ticket_id";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':ticket_id', $ticketId);
    $stmt->execute();
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}
function getTicketHistory($ticketId) {
    global $pdo;
    
    $query = "SELECT * FROM ticket_history WHERE ticket_id = :ticket_id ORDER BY created_at DESC";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':ticket_id', $ticketId);
    $stmt->execute();
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
/**
* // ดูเวลาที่ใช้ในการแก้ไขของ ticket หนึ่งๆ
* $resolution = calculateTicketResolutionTime($ticketId);
* echo "Time to resolve: " . $resolution['formatted']; // แสดงผล เช่น "05:30:15"
*
 * Calculate time difference between ticket creation and closure
 * @param int $ticketId
 * @return array Contains hours, minutes, seconds and total seconds
 */
function calculateTicketResolutionTime($ticketId) {
    global $pdo;
    
    $query = "SELECT 
        created_at,
        (SELECT updated_at 
         FROM tickets 
         WHERE id = :ticket_id AND status = 'Closed') as closed_at
        FROM tickets 
        WHERE id = :ticket_id";

    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':ticket_id', $ticketId);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$result || !$result['closed_at']) {
        return [
            'hours' => 0,
            'minutes' => 0,
            'seconds' => 0,
            'total_seconds' => 0,
            'formatted' => 'Not closed yet'
        ];
    }

    $created = new DateTime($result['created_at']);
    $closed = new DateTime($result['closed_at']);
    $interval = $created->diff($closed);
    
    $totalSeconds = ($interval->days * 24 * 60 * 60) +
                    ($interval->h * 60 * 60) +
                    ($interval->i * 60) +
                    $interval->s;

    return [
        'hours' => $interval->h + ($interval->days * 24),
        'minutes' => $interval->i,
        'seconds' => $interval->s,
        'total_seconds' => $totalSeconds,
        'formatted' => sprintf(
            '%02d:%02d:%02d',
            $interval->h + ($interval->days * 24),
            $interval->i,
            $interval->s
        )
    ];
}

/**
 * // ดูค่าเฉลี่ยเวลาในการแก้ไขทั้งหมด
 * $average = getAverageResolutionTime();
 * echo "Average resolution time: " . $average['formatted'];
 * 
 * Get average resolution time for all closed tickets
 * @return array Contains average time statistics
 */
function getAverageResolutionTime() {
    global $pdo;
    
    $query = "SELECT 
        AVG(TIMESTAMPDIFF(SECOND, created_at, updated_at)) as avg_seconds
        FROM tickets 
        WHERE status = 'Closed'";

    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$result['avg_seconds']) {
        return [
            'hours' => 0,
            'minutes' => 0,
            'seconds' => 0,
            'total_seconds' => 0,
            'formatted' => 'No closed tickets'
        ];
    }

    $totalSeconds = round($result['avg_seconds']);
    $hours = floor($totalSeconds / 3600);
    $minutes = floor(($totalSeconds % 3600) / 60);
    $seconds = $totalSeconds % 60;

    return [
        'hours' => $hours,
        'minutes' => $minutes,
        'seconds' => $seconds,
        'total_seconds' => $totalSeconds,
        'formatted' => sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds)
    ];
}

/**
 * Get CSS class for ticket table row based on priority and status
 * @param string $priority Ticket priority (High/Medium/Low)
 * @param string $status Ticket status (Open/In Progress/Pending/Closed)
 * @return string CSS class name
 */
function getTicketRowClass($priority, $status) {
    // First check status
    if ($status == 'Closed') {
        return 'table-success';
    }
    
    // Then check priority if not closed
    switch ($priority) {
        case 'High':
            return 'table-danger';
        case 'Medium':
            return 'table-warning';
        case 'Low':
            return '';
        default:
            return '';
    }
}

function getStatusBadgeClass($status) {
    switch ($status) {
        case 'Open':
            //return 'info';
            return 'danger';
        case 'In Progress':
            return 'primary';
        case 'Pending':
            return 'warning';
        case 'Closed':
            return 'success';
        default:
            return 'secondary';
    }
}
function getPriorityBadgeClass($priority) {
    switch ($priority) {
        case 'High':
            return 'danger';
        case 'Medium':
            return 'warning';
        case 'Low':
            return 'success';
        default:
            return 'secondary';
    }
}
function getUserRole($userId) {
    global $pdo;
    
    $query = "SELECT role FROM users WHERE id = :user_id";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':user_id', $userId);
    $stmt->execute();
    
    return $stmt->fetchColumn();
}
function getUserById($userId) {
    global $pdo;
    
    $query = "SELECT * FROM users WHERE id = :user_id";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':user_id', $userId);
    $stmt->execute();
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * Generate ticket number in format TKTyyyy-NNNNNNNN
 * Resets sequence number every year
 * @return string Generated ticket number
 */
function generateTicketNumberV1($pdo) {
    $currentYear = date('Y');
    try {
        // Get the last ticket number for current year
        $query = "SELECT MAX(SUBSTRING(ticket_number, 8)) as last_sequence 
                  FROM tickets 
                  WHERE SUBSTRING(ticket_number, 4, 4) = :year";
                  
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':year', $currentYear);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // If no tickets exist for this year, start with 1
        // Remove any non-numeric characters from last_sequence before converting
        $lastSequence = preg_replace('/[^0-9]/', '', $result['last_sequence']);
        $nextSequence = empty($lastSequence) ? 1 : intval($lastSequence) + 1;
        
        // Format: TKT + YYYY + "-" + 8-digit sequence padded with zeros
        $ticketNumber = sprintf("TKT%d-%08d", $currentYear, $nextSequence);
        
        return $ticketNumber;
    } catch (PDOException $e) {
        error_log("Error generating ticket number: " . $e->getMessage());
        return false;
    }
}
/**
 * Generate ticket number in format TKTyyyymm-NNNN
 * Format: TKT + year + month + '-' + running number
 * Resets sequence number every month
 * @param PDO $pdo Database connection
 * @return string Generated ticket number
 */
function generateTicketNumber($pdo) {
    $currentYear = date('Y');
    $currentMonth = date('m');
    $yearMonth = $currentYear . $currentMonth;
    
    try {
        // Get the last ticket number for current year and month
        $query = "SELECT MAX(CAST(SUBSTRING(ticket_number, 11) AS UNSIGNED)) as last_sequence 
                  FROM tickets 
                  WHERE SUBSTRING(ticket_number, 4, 6) = :year_month";
                  
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':year_month', $yearMonth);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // If no tickets exist for this year/month, start with 1
        $nextSequence = empty($result['last_sequence']) ? 1 : intval($result['last_sequence']) + 1;
        
        // Format: TKT + YYYYMM + "-" + 4-digit sequence padded with zeros
        $ticketNumber = sprintf("TKT%s-%04d", $yearMonth, $nextSequence);
        
        return $ticketNumber;
    } catch (PDOException $e) {
        error_log("Error generating ticket number by month: " . $e->getMessage());
        return false;
    }
}

/**
 * Get customer details by reference number
 * @param PDO $pdo Database connection
 * @param string $ref Reference number to search for
 * @return array Customer details or null if not found
 */
function getCustomerByRef($pdo,$ref) {
    
    $query = "SELECT Ref,CusCode,Site_Name FROM KCS_DB.Main WHERE Ref=:ref";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':ref', $ref);
    $stmt->execute();
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * Calculate total working time for a ticket excluding pending periods
 * @param string $ticket_number Ticket number to calculate time for
 * @return array Total time and formatted duration
 */
function calculateTicketWorkingTime($ticket_number) {
    global $pdo;
    
    try {
        // Get ticket creation time and status history
        $query = "SELECT 
            t.created_at as ticket_start,
            t.status as current_status,
            CASE 
                WHEN t.status = 'Closed' THEN 
                    (SELECT changed_at 
                     FROM ticket_status_history 
                     WHERE ticket_number = t.ticket_number 
                     AND status = 'Closed' 
                     ORDER BY changed_at DESC 
                     LIMIT 1)
                ELSE NULL
            END as closed_at
        FROM tickets t
        WHERE t.ticket_number = ?";
        
        $stmt = $pdo->prepare($query);
        $stmt->execute([$ticket_number]);
        $ticket = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Get all status changes
        $history_query = "SELECT 
            changed_at,
            status,
            LAG(changed_at) OVER (ORDER BY changed_at) as prev_time,
            LAG(status) OVER (ORDER BY changed_at) as prev_status
        FROM ticket_status_history
        WHERE ticket_number = ?
        ORDER BY changed_at";
        
        $history_stmt = $pdo->prepare($history_query);
        $history_stmt->execute([$ticket_number]);
        $history = $history_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $total_seconds = 0;
        $pending_seconds = 0;
        $start_time = strtotime($ticket['ticket_start']);
        
        // Calculate pending time periods
        foreach ($history as $change) {
            if ($change['prev_status'] === 'Pending') {
                $pending_seconds += strtotime($change['changed_at']) - strtotime($change['prev_time']);
            }
        }
        
        // Calculate total time
        if ($ticket['current_status'] === 'Closed' && $ticket['closed_at']) {
            $total_seconds = strtotime($ticket['closed_at']) - $start_time;
        } else {
            $total_seconds = time() - $start_time;
        }
        
        // Subtract pending time from total time
        $working_seconds = $total_seconds - $pending_seconds;
        
        // Format duration
        $duration = formatWorkingTime($working_seconds);
        
        return [
            'total_seconds' => $working_seconds,
            'formatted_duration' => $duration,
            'is_closed' => ($ticket['current_status'] === 'Closed')
        ];
    } catch (Exception $e) {
        error_log("Error calculating ticket time: " . $e->getMessage());
        return null;
    }
}

/**
 * Format seconds into human readable duration
 * @param int $seconds Total seconds to format
 * @return string Formatted duration
 */
function formatWorkingTime($seconds) {
    $days = floor($seconds / (60 * 60 * 24));
    $hours = floor(($seconds % (60 * 60 * 24)) / (60 * 60));
    $minutes = floor(($seconds % (60 * 60)) / 60);
    
    $parts = [];
    if ($days > 0) {
        $parts[] = $days . ' วัน';
    }
    if ($hours > 0) {
        $parts[] = $hours . ' ชั่วโมง';
    }
    if ($minutes > 0) {
        $parts[] = $minutes . ' นาที';
    }
    
    return !empty($parts) ? implode(' ', $parts) : 'น้อยกว่า 1 นาที';
}

/* calculateTicketWorkingTime($ticket_number)
คุณสมบัติของฟังก์ชัน:
1. คำนวณเวลาทั้งหมดตั้งแต่สร้างจนถึงปิด ticket
2. หักลบเวลาที่สถานะเป็น Pending
3. แสดงผลในรูปแบบ วัน ชั่วโมง นาที
4. รองรับ ticket ที่ยังไม่ปิด
5. เก็บประวัติการเปลี่ยนสถานะทั้งหมด
6. มีการจัดการข้อผิดพลาด
7. รองรับการคำนวณข้ามวัน
8. แสดงสถานะ Running สำหรับ ticket ที่ยังไม่ปิด
 */

function calculateWorkingTime($created_at, $closed_at, $pending_seconds, $status) {
    $start_time = strtotime($created_at);
    $end_time = $closed_at ? strtotime($closed_at) : time();
    
    // Calculate total seconds
    $total_seconds = $end_time - $start_time;
    
    // Subtract pending time
    $working_seconds = $total_seconds - $pending_seconds;
    
    // Format duration
    $days = floor($working_seconds / (60 * 60 * 24));
    $hours = floor(($working_seconds % (60 * 60 * 24)) / (60 * 60));
    $minutes = floor(($working_seconds % (60 * 60)) / 60);
    
    $parts = [];
    if ($days > 0) {
        $parts[] = $days . 'วัน';
    }
    if ($hours > 0) {
        $parts[] = $hours . 'ชม.';
    }
    if ($minutes > 0) {
        $parts[] = $minutes . 'น.';
    }
    
    return [
        'seconds' => $working_seconds,
        'formatted' => !empty($parts) ? implode(' ', $parts) : 'น้อยกว่า 1 นาที'
    ];
}
function calculateTicketWorkingTimeById($ticket_number) {
    global $pdo;
    
    try {
        // Get ticket creation time and status history
        $query = "
        SELECT 
    t.*, 
    c.CusName AS customer_name,
    tm.name AS team_name,
    u.username AS created_by,
    CASE 
        WHEN t.status = 'Closed' THEN
            (SELECT changed_at 
             FROM ticket_status_history 
             WHERE ticket_number = t.ticket_number 
             AND status = 'Closed' 
             ORDER BY changed_at DESC 
             LIMIT 1)
        ELSE NULL
    END as closed_at,
    (SELECT SUM(TIMESTAMPDIFF(SECOND, 
        tsh1.changed_at, 
        COALESCE(tsh2.changed_at, CURRENT_TIMESTAMP)))
     FROM ticket_status_history tsh1
     LEFT JOIN ticket_status_history tsh2 
        ON tsh1.ticket_number = tsh2.ticket_number
        AND tsh2.id = (
            SELECT MIN(id) 
            FROM ticket_status_history 
            WHERE ticket_number = tsh1.ticket_number 
            AND id > tsh1.id
        )
     WHERE tsh1.ticket_number = t.ticket_number
     AND tsh1.status = 'Pending'
    ) as pending_seconds
FROM 
    tickets t
    LEFT JOIN KCS_DB.Customers c ON t.customer_number = c.CusCode
    LEFT JOIN teams tm ON t.assigned_team = tm.id
    LEFT JOIN users u ON t.username = u.username
WHERE
	t.ticket_number= ? ";
        
        $stmt = $pdo->prepare($query);
        $stmt->execute([$ticket_number]);
        $ticket = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return calculateWorkingTime($ticket['created_at'], $ticket['closed_at'], $ticket['pending_seconds']??0, $ticket['status']);
      
}catch (Exception $e) {
        error_log("Error calculating ticket time: " . $e->getMessage());
        return null;
    }
}

function sendInterimEmail($to, $subject, $body) {
    
    try {
        $mail = new PHPMailer(true);
        
        // Server settings
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;  // Add these constants to your config file
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = ''; // Use '' for no encryption, 'tls' or 'ssl' as needed
        $mail->SMTPAutoTLS = false; // Disable auto TLS if not using encryption
        $mail->CharSet = 'UTF-8';
        $mail->Encoding = 'base64';
        $mail->SMTPDebug = 0; // Set to 2 for verbose debug output
        $mail->Debugoutput = 'html'; // Change to 'error' for error output
        $mail->Port = SMTP_PORT;
        
        // Recipients
        $mail->setFrom(SMTP_FROM_EMAIL, SMTP_FROM_NAME);
        
        // Handle multiple recipients
        $recipients = explode(',', $to);
        foreach ($recipients as $recipient) {
            $mail->addAddress(trim($recipient));
        }
        $mail->addCC('<EMAIL>');
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = '=?UTF-8?B?'.base64_encode($subject).'?=';// $subject;
        $mail->Body = $body;
        
        return $mail->send();
    } catch (Exception $e) {
        error_log("Email sending failed: " . $mail->ErrorInfo);
        return false;
    }
}
